# MpClipper Sync

这是一个 Obsidian 插件，用于从 S3 对象存储中同步 Markdown 文档到 Obsidian。

## 功能

- 直接从 S3 对象存储中下载 Markdown 文档
- 将文档保存到 Obsidian 库中
- 记录同步进度并保存到本地
- 支持手动和自动同步
- 左侧功能区快速同步按钮
- 同步状态控制，防止多个同步任务同时运行
- 可配置同步文件夹和同步间隔

## 安装

1. 在 Obsidian 中打开设置
2. 进入“第三方插件”选项卡
3. 禁用“安全模式”
4. 点击“浏览”按钮
5. 搜索“MpClipper Sync”并安装

或者，您可以手动安装：

1. 下载最新的发布版本
2. 解压到您的 Obsidian 库的 `.obsidian/plugins` 目录
3. 在 Obsidian 中启用插件

## 使用方法

1. 在插件设置中配置 S3 相关参数：
   - 桶名称
   - 端点
   - 区域
   - 访问密钥和密文
   - 文件路径前缀（可选）
2. 设置同步文件夹（可选）
3. 点击“测试连接”按钮测试 S3 连接
4. 点击“同步”按钮或使用左侧功能区的同步按钮开始同步

您可以选择启用自动同步并设置同步间隔。插件会自动防止多个同步任务同时运行，确保同步过程的稳定性。

## 工作原理

### S3 同步机制

MpClipper Sync 直接从 S3 对象存储中同步文档：

1. 根据上次同步的时间戳，获取新增或修改的文档
2. 按时间戳顺序下载并保存文档
3. 在本地记录最后同步的时间戳，避免重复下载

### 同步状态控制

插件实现了同步状态控制机制，防止多个同步任务同时运行。当一个同步任务正在运行时，如果用户尝试启动另一个同步任务，插件会显示“正在同步中，请稍后再试”的提示。

### 注意事项

- S3 模式需要配置适当的 S3 访问权限，允许列出和获取指定路径下的文件
- 插件仅支持同步 Markdown 文件（.md 文件扩展名）
- 如果需要同步大量文件，建议使用文件路径前缀来限定同步范围

## 许可证

MIT
