/* 文档同步器插件样式 */

/* 调整设置界面中输入框的宽度 */
.mpclipper-sync-settings .setting-item {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid var(--background-modifier-border);
  padding-top: 15px;
  padding-bottom: 15px;
}

.mpclipper-sync-settings .setting-item:last-child {
  border-bottom: none;
}

.mpclipper-sync-settings .setting-item-info {
  flex: 0 0 45%; /* 左侧标签区域占 45% */
}

.mpclipper-sync-settings .setting-item-control {
  flex: 0 0 55%; /* 右侧控件区域占 55% */
}

/* 确保输入框充满控件区域 */
.mpclipper-sync-settings input[type="text"],
.mpclipper-sync-settings input[type="password"],
.mpclipper-sync-settings select,
.mpclipper-sync-settings .dropdown {
  width: 100%;
}

/* 设置页面样式 */
.mpclipper-sync-settings .setting-item {
  border-bottom: 1px solid var(--background-modifier-border);
  padding-top: 15px;
  padding-bottom: 15px;
}

.mpclipper-sync-settings .setting-item:last-child {
  border-bottom: none;
}

.mpclipper-import-modal .modal-content {
  padding: 20px;
}

.mpclipper-warning {
  background-color: var(--background-secondary-alt);
  border-left: 3px solid var(--color-orange);
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: var(--radius-s);
}

/* 文本区域样式 */
.mpclipper-import-modal textarea {
  width: 100%;
  height: 150px;
  margin-top: 10px;
}

/* --- 公众号推广卡片样式 --- */
.mp-support-banner-container {
  background-color: var(--background-secondary);
  border-radius: var(--radius-m);
  padding: 8px;
  margin-top: 20px;
  border: 1px solid var(--background-modifier-border);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  cursor: pointer;
}

.mp-support-banner-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.mp-support-banner-image {
  width: 100%;
  display: block;
  border-radius: calc(var(--radius-m) - 4px); /* 内外圆角保持协调 */
}

/* --- 新增：全局头部样式 --- */
.plugin-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--background-modifier-border);
  margin-bottom: 20px;
}

.plugin-header h1 {
  font-size: 1.8em;
  font-weight: 600;
  color: var(--text-normal);
  margin-bottom: 10px;
}

.plugin-header p {
  font-size: 1em;
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
}

/* --- 新增：选项卡导航样式 --- */
.nav-container {
  display: flex;
  border-bottom: 1px solid var(--background-modifier-border);
  margin-bottom: 20px;
}

.nav-button {
  background: none;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 1em;
  border-bottom: 2px solid transparent;
  margin-bottom: -1px; /* 对齐容器边框 */
  color: var(--text-muted);
  transition: color 0.2s, border-color 0.2s;
}

.nav-button:hover {
  color: var(--text-normal);
}

.nav-button.active {
  border-bottom-color: var(--interactive-accent);
  color: var(--interactive-accent-hover);
}

/* --- 新增：FAQ 页面样式 --- */
.faq-item {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--background-modifier-border);
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item h3 {
  margin-bottom: 15px;
  color: var(--text-normal);
}

.faq-item p {
  line-height: 1.7;
  color: var(--text-muted);
}

.faq-item code {
  background-color: var(--background-modifier-hover);
  padding: 2px 5px;
  border-radius: var(--radius-s);
  font-size: 0.9em;
  color: var(--text-normal);
}

.faq-image {
  max-width: 100%;
  border-radius: var(--radius-m);
  border: 1px solid var(--background-modifier-border);
  margin-top: 15px;
}
