{"name": "obsidian-document-syncer", "version": "1.0.0", "description": "从服务器同步对象存储中的Markdown文档", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": ["obsidian", "plugin", "markdown", "sync"], "author": "Augment Agent", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.821.0", "ali-oss": "^6.23.0", "cos-js-sdk-v5": "^1.10.1"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "obsidian": "latest", "tslib": "2.4.0", "typescript": "4.7.4"}}