import OSS from 'ali-oss';
import { ICloudStorageClient } from './ICloudStorageClient';
import { DocumentSyncerSettings, S3Object } from './types';
import { Notice } from 'obsidian';

/**
 * 阿里云 OSS 客户端实现
 */
export class AliyunOSSClient implements ICloudStorageClient {
    private client: OSS | null = null;
    private settings: DocumentSyncerSettings;
    
    /**
     * 构造函数
     * @param settings 插件设置
     */
    constructor(settings: DocumentSyncerSettings) {
        this.settings = settings;
    }
    
    /**
     * 获取 OSS 客户端实例
     * 延迟初始化，只在需要时才创建客户端
     * @returns OSS 实例
     */
    private getClient(): OSS {
        if (!this.client) {
            // 从 endpoint 中提取 region 信息
            const region = this.extractRegionFromEndpoint(this.settings.s3Endpoint);
            
            this.client = new OSS({
                region: region,
                accessKeyId: this.settings.s3AccessKey,
                accessKeySecret: this.settings.s3AccessSecret,
                bucket: this.settings.s3BucketName,
                endpoint: this.settings.s3Endpoint
            });
        }
        return this.client;
    }
    
    /**
     * 从 endpoint 中提取 region 信息
     * @param endpoint OSS endpoint
     * @returns region 字符串
     */
    private extractRegionFromEndpoint(endpoint: string): string {
        // 阿里云 OSS endpoint 格式：oss-cn-hangzhou.aliyuncs.com
        const match = endpoint.match(/oss-([^.]+)\.aliyuncs\.com/);
        return match ? match[1] : 'cn-hangzhou'; // 默认使用杭州区域
    }
    
    /**
     * 测试 OSS 连接
     * @returns 连接是否成功
     */
    async testConnection(): Promise<boolean> {
        try {
            if (!this.validateSettings()) {
                return false;
            }
            
            // 尝试列出一个对象，测试连接
            const result = await this.getClient().list({
                prefix: this.settings.s3FilePath,
                'max-keys': 1
            }, {});
            
            return true;
        } catch (error) {
            console.warn('阿里云 OSS 连接测试失败:', error);
            return false;
        }
    }
    
    /**
     * 获取指定时间戳之后的文件列表
     * @param startTimestamp 开始时间戳（毫秒）
     * @returns 对象列表
     */
    async listFiles(startTimestamp: number): Promise<S3Object[]> {
        try {
            if (!this.validateSettings()) {
                return [];
            }
            
            // 优化配置：减少单次请求大小，限制最大页数
            const PAGE_SIZE = 100; // OSS 支持更大的页面大小
            const MAX_PAGES = 20;
            
            const s3Objects: S3Object[] = [];
            let marker: string | undefined = undefined;
            let pageCount = 0;
            
            console.log(`开始增量同步，起始时间戳: ${new Date(startTimestamp).toISOString()}`);
            
            // 处理分页结果，使用优化的增量同步策略
            while (pageCount < MAX_PAGES) {
                const listParams: any = {
                    prefix: this.settings.s3FilePath,
                    'max-keys': PAGE_SIZE
                };
                
                if (marker) {
                    listParams.marker = marker;
                }
                
                const result = await this.getClient().list(listParams, {});
                const objects = result.objects || [];
                
                let newFilesInPage = 0;
                
                // 处理当前页的对象
                for (const obj of objects) {
                    if (obj.name && obj.lastModified && obj.size) {
                        // 只处理 Markdown 文件
                        if (!obj.name.endsWith('.md')) {
                            continue;
                        }
                        
                        // OSS 返回的 lastModified 是 ISO 字符串，需要转换为 Date
                        const lastModified = new Date(obj.lastModified);
                        
                        // 只处理指定时间戳之后的文件
                        if (lastModified.getTime() > startTimestamp) {
                            s3Objects.push({
                                key: obj.name,
                                lastModified: lastModified,
                                size: obj.size
                            });
                            newFilesInPage++;
                        }
                    }
                }
                
                console.log(`页面 ${pageCount + 1}: 处理 ${objects.length} 个对象，找到 ${newFilesInPage} 个新文件`);
                
                // 如果这一页没有新文件且不是第一页，可能后续页面也没有
                if (newFilesInPage === 0 && pageCount > 0) {
                    console.log('连续页面无新文件，提前终止同步');
                    break;
                }
                
                // 检查是否有更多页
                const isTruncated = result.isTruncated || false;
                if (!isTruncated) {
                    console.log('已到达最后一页');
                    break;
                }
                
                if (result.nextMarker) {
                    marker = result.nextMarker;
                }
                pageCount++;
            }
            
            if (pageCount >= MAX_PAGES) {
                console.warn(`达到最大页数限制 (${MAX_PAGES})，可能还有更多文件未同步`);
                new Notice(`已同步前 ${MAX_PAGES} 页文件，如需同步更多文件，请再次执行同步`);
            }
            
            // 按时间戳排序
            s3Objects.sort((a, b) => a.lastModified.getTime() - b.lastModified.getTime());
            
            console.log(`同步完成，共找到 ${s3Objects.length} 个新文件`);
            return s3Objects;
        } catch (error) {
            console.error('获取阿里云 OSS 文件列表失败:', error);
            new Notice(`获取文件列表失败: ${error.message}`);
            return [];
        }
    }
    
    /**
     * 下载文件内容
     * @param key 文件键名
     * @returns 文件内容
     */
    async downloadFile(key: string): Promise<string | null> {
        try {
            if (!this.validateSettings()) {
                return null;
            }
            
            const result = await this.getClient().get(key);
            
            // OSS 返回的 content 是 Buffer，需要转换为字符串
            if (result.content) {
                return result.content.toString('utf-8');
            }
            
            return null;
        } catch (error) {
            console.warn(`下载阿里云 OSS 文件 ${key} 失败:`, error);
            new Notice(`下载文件失败: ${error.message}`);
            return null;
        }
    }
    
    /**
     * 验证 OSS 设置是否完整
     * @returns 设置是否有效
     */
    private validateSettings(): boolean {
        if (!this.settings.s3BucketName) {
            new Notice('OSS 桶名称未配置');
            return false;
        }
        
        if (!this.settings.s3Endpoint) {
            new Notice('OSS 端点未配置');
            return false;
        }
        
        if (!this.settings.s3AccessKey || !this.settings.s3AccessSecret) {
            new Notice('OSS 访问密钥未配置');
            return false;
        }
        
        return true;
    }
} 