import { 
    S3Client, 
    ListObjectsV2Command, 
    ListObjectsV2CommandInput, 
    GetObjectCommand,
    GetObjectCommandInput
} from "@aws-sdk/client-s3";
import { ICloudStorageClient } from './ICloudStorageClient';
import { DocumentSyncerSettings, S3Object } from './types';
import { Notice } from 'obsidian';

/**
 * AWS S3 客户端实现
 */
export class AwsS3Client implements ICloudStorageClient {
    private client: S3Client | null = null;
    private settings: DocumentSyncerSettings;
    
    /**
     * 构造函数
     * @param settings 插件设置
     */
    constructor(settings: DocumentSyncerSettings) {
        this.settings = settings;
    }
    
    /**
     * 获取 S3 客户端实例
     * 延迟初始化，只在需要时才创建客户端
     * @returns S3Client 实例
     */
    private getClient(): S3Client {
        if (!this.client) {
            // 检查是否是 Cloudflare R2
            const isCloudflareR2 = this.settings.s3Endpoint?.includes('r2.cloudflarestorage.com');

            // 确保密钥格式正确，避免 Web Crypto API 错误
            const accessKeyId = this.ensureStringFormat(this.settings.s3AccessKey);
            const secretAccessKey = this.ensureStringFormat(this.settings.s3AccessSecret);

            this.client = new S3Client({
                region: this.settings.s3Region || (isCloudflareR2 ? 'auto' : 'us-east-1'),
                endpoint: this.settings.s3Endpoint,
                credentials: {
                    accessKeyId: accessKeyId,
                    secretAccessKey: secretAccessKey
                },
                // 为 Cloudflare R2 添加特殊配置
                ...(isCloudflareR2 && {
                    forcePathStyle: true, // R2 要求使用路径样式
                    requestHandler: {
                        // 添加自定义请求头以帮助处理 CORS
                        requestTimeout: 30000,
                        httpsAgent: undefined
                    }
                }),
                // 添加浏览器环境兼容性配置
                requestHandler: {
                    requestTimeout: 30000,
                    // 确保在 Obsidian 环境中正确处理请求
                    ...(isCloudflareR2 ? { httpsAgent: undefined } : {})
                }
            });
        }
        return this.client;
    }

    /**
     * 确保字符串格式正确，避免 Web Crypto API 错误
     * @param value 输入值
     * @returns 格式化后的字符串
     */
    private ensureStringFormat(value: string): string {
        if (!value) {
            return '';
        }

        // 如果是字符串，直接返回
        if (typeof value === 'string') {
            return value.trim();
        }

        // 如果是其他类型，转换为字符串
        return String(value).trim();
    }
    
    /**
     * 测试 S3 连接
     * @returns 连接是否成功
     */
    async testConnection(): Promise<boolean> {
        try {
            if (!this.validateSettings()) {
                return false;
            }

            // 尝试列出一个对象，只获取一个结果，测试连接
            const params: ListObjectsV2CommandInput = {
                Bucket: this.settings.s3BucketName,
                MaxKeys: 1,
                Prefix: this.settings.s3FilePath
            };

            const command = new ListObjectsV2Command(params);
            await this.getClient().send(command);

            return true;
        } catch (error) {
            console.warn('AWS S3 连接测试失败:', error);

            // 检查是否是 Web Crypto API 相关错误
            if (error.message && error.message.includes('SubtleCrypto')) {
                new Notice('加密API错误：请检查访问密钥格式是否正确，或尝试重新配置密钥。');
                console.error('Web Crypto API 错误详情:', error);
            } else if (error.message && error.message.includes('CORS')) {
                new Notice('CORS 错误：请配置存储桶的跨域访问策略。详情请查看插件文档。');
            } else if (error.name === 'NetworkError' || error.message.includes('fetch')) {
                new Notice('网络连接错误：请检查网络连接和存储桶配置。');
            } else {
                new Notice(`连接测试失败: ${error.message}`);
            }

            return false;
        }
    }
    
    /**
     * 获取指定时间戳之后的文件列表
     * @param startTimestamp 开始时间戳（毫秒）
     * @returns S3 对象列表
     */
    async listFiles(startTimestamp: number): Promise<S3Object[]> {
        try {
            if (!this.validateSettings()) {
                return [];
            }
            
            // 优化配置：减少单次请求大小，限制最大页数
            const PAGE_SIZE = 50;
            const MAX_PAGES = 20;
            
            const s3Objects: S3Object[] = [];
            let continuationToken: string | undefined = undefined;
            let pageCount = 0;
            
            console.log(`开始增量同步，起始时间戳: ${new Date(startTimestamp).toISOString()}`);
            
            // 处理分页结果，使用优化的增量同步策略
            while (pageCount < MAX_PAGES) {
                const params: ListObjectsV2CommandInput = {
                    Bucket: this.settings.s3BucketName,
                    Prefix: this.settings.s3FilePath,
                    MaxKeys: PAGE_SIZE,
                    ...(continuationToken && { ContinuationToken: continuationToken })
                };
                
                const response = await this.getClient().send(new ListObjectsV2Command(params));
                const objects = response.Contents || [];
                
                let newFilesInPage = 0;
                
                // 处理当前页的对象
                for (const obj of objects) {
                    if (obj.Key && obj.LastModified && obj.Size) {
                        // 只处理 Markdown 文件
                        if (!obj.Key.endsWith('.md')) {
                            continue;
                        }
                        
                        // 只处理指定时间戳之后的文件
                        if (obj.LastModified.getTime() > startTimestamp) {
                            s3Objects.push({
                                key: obj.Key,
                                lastModified: obj.LastModified,
                                size: obj.Size
                            });
                            newFilesInPage++;
                        }
                    }
                }
                
                console.log(`页面 ${pageCount + 1}: 处理 ${objects.length} 个对象，找到 ${newFilesInPage} 个新文件`);
                
                // 如果这一页没有新文件且不是第一页，可能后续页面也没有
                if (newFilesInPage === 0 && pageCount > 0) {
                    console.log('连续页面无新文件，提前终止同步');
                    break;
                }
                
                // 检查是否有更多页
                if (!response.IsTruncated) {
                    console.log('已到达最后一页');
                    break;
                }
                
                continuationToken = response.NextContinuationToken;
                pageCount++;
            }
            
            if (pageCount >= MAX_PAGES) {
                console.warn(`达到最大页数限制 (${MAX_PAGES})，可能还有更多文件未同步`);
                new Notice(`已同步前 ${MAX_PAGES} 页文件，如需同步更多文件，请再次执行同步`);
            }
            
            // 按时间戳排序
            s3Objects.sort((a, b) => a.lastModified.getTime() - b.lastModified.getTime());
            
            console.log(`同步完成，共找到 ${s3Objects.length} 个新文件`);
            return s3Objects;
        } catch (error) {
            console.error('获取 AWS S3 文件列表失败:', error);

            // 提供更友好的错误信息
            if (error.message && error.message.includes('SubtleCrypto')) {
                new Notice('❌ 加密API错误：密钥格式可能不正确。请检查访问密钥和密钥格式，或尝试重新输入密钥。');
                console.error('Web Crypto API 错误详情:', {
                    message: error.message,
                    stack: error.stack,
                    accessKeyLength: this.settings.s3AccessKey?.length,
                    secretKeyLength: this.settings.s3AccessSecret?.length
                });
            } else if (error.message && error.message.includes('CORS')) {
                new Notice('CORS 错误：请配置存储桶的跨域访问策略。详情请查看插件文档。');
            } else if (error.name === 'NetworkError' || error.message.includes('fetch')) {
                new Notice('网络连接错误：请检查网络连接和存储桶配置。');
            } else {
                new Notice(`获取文件列表失败: ${error.message}`);
            }

            return [];
        }
    }
    
    /**
     * 下载文件内容
     * @param key 文件键名
     * @returns 文件内容
     */
    async downloadFile(key: string): Promise<string | null> {
        try {
            if (!this.validateSettings()) {
                return null;
            }
            
            const params: GetObjectCommandInput = {
                Bucket: this.settings.s3BucketName,
                Key: key
            };
            
            const command = new GetObjectCommand(params);
            const response = await this.getClient().send(command);
            
            // 读取文件内容
            if (response.Body) {
                const streamReader = response.Body.transformToString();
                return await streamReader;
            }
            
            return null;
        } catch (error) {
            console.warn(`下载 AWS S3 文件 ${key} 失败:`, error);
            
            // 提供更友好的错误信息
            if (error.message && error.message.includes('CORS')) {
                new Notice('CORS 错误：请配置存储桶的跨域访问策略。详情请查看插件文档。');
            } else if (error.name === 'NetworkError' || error.message.includes('fetch')) {
                new Notice('网络连接错误：请检查网络连接和存储桶配置。');
            } else {
                new Notice(`下载文件失败: ${error.message}`);
            }
            
            return null;
        }
    }
    
    /**
     * 验证 S3 设置是否完整
     * @returns 设置是否有效
     */
    private validateSettings(): boolean {
        if (!this.settings.s3BucketName) {
            new Notice('S3 桶名称未配置');
            return false;
        }

        if (!this.settings.s3Endpoint) {
            new Notice('S3 端点未配置');
            return false;
        }

        if (!this.settings.s3AccessKey || !this.settings.s3AccessSecret) {
            new Notice('S3 访问密钥未配置');
            return false;
        }

        // 验证密钥格式
        if (!this.validateKeyFormat(this.settings.s3AccessKey, 'Access Key')) {
            return false;
        }

        if (!this.validateKeyFormat(this.settings.s3AccessSecret, 'Secret Key')) {
            return false;
        }

        return true;
    }

    /**
     * 验证密钥格式是否正确
     * @param key 密钥值
     * @param keyName 密钥名称（用于错误提示）
     * @returns 是否有效
     */
    private validateKeyFormat(key: string, keyName: string): boolean {
        if (!key || typeof key !== 'string') {
            new Notice(`${keyName} 格式无效：必须是非空字符串`);
            return false;
        }

        // 检查是否包含非法字符（可能导致 Web Crypto API 错误）
        const trimmedKey = key.trim();
        if (trimmedKey !== key) {
            console.warn(`${keyName} 包含前后空格，已自动清理`);
        }

        if (trimmedKey.length === 0) {
            new Notice(`${keyName} 不能为空`);
            return false;
        }

        // AWS Access Key 通常是 20 个字符，Secret Key 通常是 40 个字符
        // 但这里只做基本长度检查，避免过于严格
        if (keyName === 'Access Key' && trimmedKey.length < 16) {
            new Notice(`${keyName} 长度过短，请检查是否完整`);
            return false;
        }

        if (keyName === 'Secret Key' && trimmedKey.length < 32) {
            new Notice(`${keyName} 长度过短，请检查是否完整`);
            return false;
        }

        return true;
    }
} 