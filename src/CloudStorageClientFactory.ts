import { ICloudStorageClient } from './ICloudStorageClient';
import { AwsS3Client } from './AwsS3Client';
import { AliyunOSSClient } from './AliyunOSSClient';
import { TencentCOSClient } from './TencentCOSClient';
import { DocumentSyncerSettings } from './types';

/**
 * 云存储提供商类型
 */
export type CloudProvider = 'aws' | 'aliyun' | 'tencent';

/**
 * 云存储客户端工厂类
 * 根据 endpoint 自动判断并创建对应的客户端实例
 */
export class CloudStorageClientFactory {
    
    /**
     * 根据设置创建对应的云存储客户端
     * @param settings 插件设置
     * @returns 云存储客户端实例
     */
    static createClient(settings: DocumentSyncerSettings): ICloudStorageClient {
        const provider = this.detectCloudProvider(settings.s3Endpoint);
        
        console.log(`检测到云存储提供商: ${provider}, endpoint: ${settings.s3Endpoint}`);
        
        switch (provider) {
            case 'aliyun':
                return new AliyunOSSClient(settings);
            case 'tencent':
                return new TencentCOSClient(settings);
            case 'aws':
            default:
                return new AwsS3Client(settings);
        }
    }
    
    /**
     * 根据 endpoint 检测云存储提供商
     * @param endpoint 存储服务端点
     * @returns 云存储提供商类型
     */
    private static detectCloudProvider(endpoint: string): CloudProvider {
        if (!endpoint) {
            return 'aws'; // 默认使用 AWS S3
        }
        
        const lowerEndpoint = endpoint.toLowerCase();
        
        // 检测阿里云 OSS
        if (lowerEndpoint.includes('aliyuncs.com') || 
            lowerEndpoint.includes('oss-') ||
            lowerEndpoint.match(/oss[.-]/)) {
            return 'aliyun';
        }
        
        // 检测腾讯云 COS
        if (lowerEndpoint.includes('myqcloud.com') || 
            lowerEndpoint.includes('cos.') ||
            lowerEndpoint.match(/cos[.-]/)) {
            return 'tencent';
        }
        
        // 默认使用 AWS S3 兼容接口
        return 'aws';
    }
    
    /**
     * 获取提供商名称（用于日志显示）
     * @param provider 提供商类型
     * @returns 提供商中文名称
     */
    static getProviderName(provider: CloudProvider): string {
        switch (provider) {
            case 'aliyun':
                return '阿里云 OSS';
            case 'tencent':
                return '腾讯云 COS';
            case 'aws':
            default:
                return 'AWS S3';
        }
    }
    
    /**
     * 检测当前使用的云存储提供商
     * @param endpoint 存储服务端点
     * @returns 提供商类型
     */
    static detectProvider(endpoint: string): CloudProvider {
        return this.detectCloudProvider(endpoint);
    }
} 