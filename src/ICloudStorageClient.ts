import { S3Object } from './types';

/**
 * 云存储客户端统一接口
 * 支持 AWS S3、阿里云 OSS、腾讯云 COS
 */
export interface ICloudStorageClient {
    /**
     * 测试连接
     * @returns 连接是否成功
     */
    testConnection(): Promise<boolean>;

    /**
     * 获取指定时间戳之后的文件列表
     * @param startTimestamp 开始时间戳（毫秒）
     * @returns 对象列表
     */
    listFiles(startTimestamp: number): Promise<S3Object[]>;

    /**
     * 下载文件内容
     * @param key 文件键名
     * @returns 文件内容
     */
    downloadFile(key: string): Promise<string | null>;
} 