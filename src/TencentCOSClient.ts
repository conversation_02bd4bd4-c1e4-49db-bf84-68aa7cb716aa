// @ts-ignore - cos-js-sdk-v5 没有类型定义
import COS from 'cos-js-sdk-v5';
import { ICloudStorageClient } from './ICloudStorageClient';
import { DocumentSyncerSettings, S3Object } from './types';
import { Notice } from 'obsidian';

/**
 * 腾讯云 COS 客户端实现（浏览器版本）
 * 使用 cos-js-sdk-v5 支持浏览器环境
 */
export class TencentCOSClient implements ICloudStorageClient {
    private client: COS | null = null;
    private settings: DocumentSyncerSettings;
    
    /**
     * 构造函数
     * @param settings 插件设置
     */
    constructor(settings: DocumentSyncerSettings) {
        this.settings = settings;
    }
    
    /**
     * 获取 COS 客户端实例
     * 延迟初始化，只在需要时才创建客户端
     * @returns COS 实例
     */
    private getClient(): COS {
        if (!this.client) {
            // cos-js-sdk-v5 的初始化方式
            this.client = new COS({
                SecretId: this.settings.s3AccessKey,
                SecretKey: this.settings.s3AccessSecret,
                // 浏览器环境下不需要设置其他配置
            });
        }
        return this.client;
    }
    
    /**
     * 从 endpoint 中提取 region 信息
     * @param endpoint COS endpoint
     * @returns region 字符串
     */
    private extractRegionFromEndpoint(endpoint: string): string {
        // 腾讯云 COS endpoint 格式：cos.ap-beijing.myqcloud.com
        const match = endpoint.match(/cos\.([^.]+)\.myqcloud\.com/);
        return match ? match[1] : 'ap-beijing'; // 默认使用北京区域
    }
    
    /**
     * 测试 COS 连接
     * @returns 连接是否成功
     */
    async testConnection(): Promise<boolean> {
        try {
            if (!this.validateSettings()) {
                return false;
            }
            
            const region = this.extractRegionFromEndpoint(this.settings.s3Endpoint);
            
            // 使用 cos-js-sdk-v5 的 API
            return new Promise((resolve) => {
                this.getClient().getBucket({
                    Bucket: this.settings.s3BucketName,
                    Region: region,
                    Prefix: this.settings.s3FilePath,
                    MaxKeys: 1
                }, (err: any, data: any) => {
                    if (err) {
                        console.warn('腾讯云 COS 连接测试失败:', err);
                        resolve(false);
                    } else {
                        console.log('腾讯云 COS 连接测试成功');
                        resolve(true);
                    }
                });
            });
        } catch (error) {
            console.warn('腾讯云 COS 连接测试失败:', error);
            return false;
        }
    }
    
    /**
     * 获取指定时间戳之后的文件列表
     * @param startTimestamp 开始时间戳（毫秒）
     * @returns 对象列表
     */
    async listFiles(startTimestamp: number): Promise<S3Object[]> {
        try {
            if (!this.validateSettings()) {
                return [];
            }
            
            // 优化配置：减少单次请求大小，限制最大页数
            const PAGE_SIZE = 100; // COS 支持更大的页面大小
            const MAX_PAGES = 20;
            
            const region = this.extractRegionFromEndpoint(this.settings.s3Endpoint);
            const s3Objects: S3Object[] = [];
            let marker: string | undefined = undefined;
            let pageCount = 0;
            
            console.log(`开始增量同步，起始时间戳: ${new Date(startTimestamp).toISOString()}`);
            
            // 处理分页结果，使用优化的增量同步策略
            while (pageCount < MAX_PAGES) {
                const listParams: any = {
                    Bucket: this.settings.s3BucketName,
                    Region: region,
                    Prefix: this.settings.s3FilePath,
                    MaxKeys: PAGE_SIZE
                };
                
                if (marker) {
                    listParams.Marker = marker;
                }
                
                // 使用 Promise 包装回调函数
                const result = await new Promise<any>((resolve, reject) => {
                    this.getClient().getBucket(listParams, (err: any, data: any) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(data);
                        }
                    });
                });
                
                const objects = result.Contents || [];
                
                let newFilesInPage = 0;
                
                // 处理当前页的对象
                for (const obj of objects) {
                    if (obj.Key && obj.LastModified && obj.Size) {
                        // 只处理 Markdown 文件
                        if (!obj.Key.endsWith('.md')) {
                            continue;
                        }
                        
                        // COS 返回的 LastModified 是 ISO 字符串，需要转换为 Date
                        const lastModified = new Date(obj.LastModified);
                        
                        // 只处理指定时间戳之后的文件
                        if (lastModified.getTime() > startTimestamp) {
                            s3Objects.push({
                                key: obj.Key,
                                lastModified: lastModified,
                                size: parseInt(obj.Size) || 0 // 将字符串转换为数字
                            });
                            newFilesInPage++;
                        }
                    }
                }
                
                console.log(`页面 ${pageCount + 1}: 处理 ${objects.length} 个对象，找到 ${newFilesInPage} 个新文件`);
                
                // 如果这一页没有新文件且不是第一页，可能后续页面也没有
                if (newFilesInPage === 0 && pageCount > 0) {
                    console.log('连续页面无新文件，提前终止同步');
                    break;
                }
                
                // 检查是否有更多页
                const isTruncated = result.IsTruncated === 'true';
                if (!isTruncated) {
                    console.log('已到达最后一页');
                    break;
                }
                
                if (result.NextMarker) {
                    marker = result.NextMarker;
                }
                pageCount++;
            }
            
            if (pageCount >= MAX_PAGES) {
                console.warn(`达到最大页数限制 (${MAX_PAGES})，可能还有更多文件未同步`);
                new Notice(`已同步前 ${MAX_PAGES} 页文件，如需同步更多文件，请再次执行同步`);
            }
            
            // 按时间戳排序
            s3Objects.sort((a, b) => a.lastModified.getTime() - b.lastModified.getTime());
            
            console.log(`同步完成，共找到 ${s3Objects.length} 个新文件`);
            return s3Objects;
        } catch (error) {
            console.error('获取腾讯云 COS 文件列表失败:', error);
            new Notice(`获取文件列表失败: ${error.message}`);
            return [];
        }
    }
    
    /**
     * 下载文件内容
     * @param key 文件键名
     * @returns 文件内容
     */
    async downloadFile(key: string): Promise<string | null> {
        try {
            if (!this.validateSettings()) {
                return null;
            }
            
            const region = this.extractRegionFromEndpoint(this.settings.s3Endpoint);
            
            // 使用 Promise 包装回调函数
            const result = await new Promise<any>((resolve, reject) => {
                this.getClient().getObject({
                    Bucket: this.settings.s3BucketName,
                    Region: region,
                    Key: key
                }, (err: any, data: any) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(data);
                    }
                });
            });
            
            // cos-js-sdk-v5 返回的 Body 需要特殊处理
            if (result.Body) {
                // 在浏览器环境中，Body 通常是字符串
                return typeof result.Body === 'string' ? result.Body : result.Body.toString();
            }
            
            return null;
        } catch (error) {
            console.warn(`下载腾讯云 COS 文件 ${key} 失败:`, error);
            new Notice(`下载文件失败: ${error.message}`);
            return null;
        }
    }
    
    /**
     * 验证 COS 设置是否完整
     * @returns 设置是否有效
     */
    private validateSettings(): boolean {
        if (!this.settings.s3BucketName) {
            new Notice('COS 桶名称未配置');
            return false;
        }
        
        if (!this.settings.s3Endpoint) {
            new Notice('COS 端点未配置');
            return false;
        }
        
        if (!this.settings.s3AccessKey || !this.settings.s3AccessSecret) {
            new Notice('COS 访问密钥未配置');
            return false;
        }
        
        return true;
    }
} 