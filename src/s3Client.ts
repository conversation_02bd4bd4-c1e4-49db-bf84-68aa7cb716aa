import { ICloudStorageClient } from './ICloudStorageClient';
import { CloudStorageClientFactory } from './CloudStorageClientFactory';
import { DocumentSyncerSettings, S3Object } from './types';

/**
 * 云存储 API 客户端
 * 支持 AWS S3、阿里云 OSS、腾讯云 COS
 * 根据 endpoint 自动选择对应的实现
 */
export class S3ApiClient {
    private client: ICloudStorageClient | null = null;
    private settings: DocumentSyncerSettings;
    
    /**
     * 构造函数
     * @param settings 插件设置
     */
    constructor(settings: DocumentSyncerSettings) {
        this.settings = settings;
        // 不在构造函数中初始化 S3Client，而是在需要时才初始化
    }
    
    /**
     * 获取云存储客户端实例
     * 延迟初始化，只在需要时才创建客户端
     * @returns 云存储客户端实例
     */
    private getClient(): ICloudStorageClient {
        if (!this.client) {
            // 使用工厂模式创建对应的客户端
            this.client = CloudStorageClientFactory.createClient(this.settings);
        }
        return this.client;
    }
    
    /**
     * 测试连接
     * @returns 连接是否成功
     */
    async testConnection(): Promise<boolean> {
        return await this.getClient().testConnection();
    }
    
    /**
     * 获取指定时间戳之后的文件列表
     * @param startTimestamp 开始时间戳（毫秒）
     * @returns 对象列表
     */
    async listFiles(startTimestamp: number): Promise<S3Object[]> {
        return await this.getClient().listFiles(startTimestamp);
    }
    
    /**
     * 下载文件内容
     * @param key 文件键名
     * @returns 文件内容
     */
    async downloadFile(key: string): Promise<string | null> {
        return await this.getClient().downloadFile(key);
    }
}
