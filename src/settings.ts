import { App, Notice, PluginSettingTab, Setting, Modal } from 'obsidian';
import DocumentSyncerPlugin from '../main';
import { S3ApiClient } from './s3Client';
import { DocumentSyncerSettings } from './types';

export class MainSettingTab extends PluginSettingTab {
    plugin: DocumentSyncerPlugin;

    constructor(app: App, plugin: DocumentSyncerPlugin) {
        super(app, plugin);
        this.plugin = plugin;
    }

    display(): void {
        const { containerEl } = this;
        containerEl.empty();
        containerEl.addClass('mpclipper-settings-container');

        // --- Plugin Header ---
        const header = containerEl.createDiv('plugin-header');
        header.createEl('h1', { text: 'MpClipper Sync' });
        header.createEl('p', { text: '很高兴你在使用我构建的这个工具，如遇到问题或者你有很棒的想法，请关注底部公众号反馈给我哟。' });

        // --- Tab Navigation ---
        const navContainer = containerEl.createDiv('nav-container');
        const settingsButton = navContainer.createEl('button', { text: '配置', cls: 'nav-button active' });
        const faqButton = navContainer.createEl('button', { text: '常见问题', cls: 'nav-button' });

        // --- Content Container ---
        const contentContainer = containerEl.createDiv('content-container');

        const clearContent = () => contentContainer.empty();
        const setActiveTab = (button: HTMLButtonElement) => {
            settingsButton.removeClass('active');
            faqButton.removeClass('active');
            button.addClass('active');
        };

        settingsButton.onClickEvent(() => {
            clearContent();
            setActiveTab(settingsButton);
            this.displaySettings(contentContainer);
        });

        faqButton.onClickEvent(() => {
            clearContent();
            setActiveTab(faqButton);
            this.displayFaq(contentContainer);
        });

        // Display default tab
        this.displaySettings(contentContainer);
    }
    
    displaySettings(containerEl: HTMLElement): void {
        containerEl.empty();

        containerEl.createEl('h2', { text: 'MpClipper Sync 配置' });
        
        containerEl.createEl('h3', { text: '高级设置' });

        new Setting(containerEl)
            .setName('备份/恢复配置')
            .setDesc('通过导出和导入JSON配置，快速备份和恢复您的设置。')
            .addButton(button => button
                .setButtonText('备份当前配置')
                .onClick(() => this.onBackup()))
            .addButton(button => button
                .setButtonText('从剪贴板导入')
                .onClick(() => this.onImport())
                .setWarning());

        containerEl.createEl('h3', { text: 'S3 对象存储设置' });
        
        new Setting(containerEl)
            .setName('S3 桶名称')
            .setDesc('存储文档的 S3 桶名称')
            .addText(text => text
                .setPlaceholder('my-documents-bucket')
                .setValue(this.plugin.settings.s3BucketName)
                .onChange(async (value) => {
                    this.plugin.settings.s3BucketName = value;
                    await this.plugin.saveSettings();
                }));
        
        new Setting(containerEl)
            .setName('S3 端点')
            .setDesc('S3 服务的端点 URL')
            .addText(text => text
                .setPlaceholder('https://s3.amazonaws.com')
                .setValue(this.plugin.settings.s3Endpoint)
                .onChange(async (value) => {
                    this.plugin.settings.s3Endpoint = value;
                    await this.plugin.saveSettings();
                }));
        
        new Setting(containerEl)
            .setName('区域')
            .setDesc('S3 区域（如 us-east-1）')
            .addText(text => text
                .setPlaceholder('us-east-1')
                .setValue(this.plugin.settings.s3Region)
                .onChange(async (value) => {
                    this.plugin.settings.s3Region = value;
                    await this.plugin.saveSettings();
                }));
            
        new Setting(containerEl)
            .setName('访问密钥 ID')
            .setDesc('S3 访问密钥 ID')
            .addText(text => text
                .setPlaceholder('AKIAIOSFODNN7EXAMPLE')
                .setValue(this.plugin.settings.s3AccessKey)
                .onChange(async (value) => {
                    this.plugin.settings.s3AccessKey = value;
                    await this.plugin.saveSettings();
                }));
        
        new Setting(containerEl)
            .setName('访问密钥密文')
            .setDesc('S3 访问密钥密文')
            .addText(text => {
                const textEl = text.inputEl as HTMLInputElement;
                textEl.type = 'password';
                
                return text
                    .setPlaceholder('访问密钥密文')
                    .setValue(this.plugin.settings.s3AccessSecret)
                    .onChange(async (value: string) => {
                        this.plugin.settings.s3AccessSecret = value;
                        await this.plugin.saveSettings();
                    });
            });
        
        new Setting(containerEl)
            .setName('文件路径前缀')
            .setDesc('要同步的文件路径前缀（可选）')
            .addText(text => text
                .setPlaceholder('documents/')
                .setValue(this.plugin.settings.s3FilePath)
                .onChange(async (value) => {
                    this.plugin.settings.s3FilePath = value;
                    await this.plugin.saveSettings();
                }));

        new Setting(containerEl)
            .setName('同步文件夹')
            .setDesc('文档将被同步到的文件夹路径')
            .addText(text => text
                .setPlaceholder('文档/同步')
                .setValue(this.plugin.settings.syncFolder)
                .onChange(async (value) => {
                    this.plugin.settings.syncFolder = value;
                    await this.plugin.saveSettings();
                }));
        
        new Setting(containerEl)
            .setName('按日期分组')
            .setDesc('开启后，将根据文件在对象存储中的原始路径创建子文件夹。')
            .addToggle(toggle => toggle
                .setValue(this.plugin.settings.groupByDate)
                .onChange(async (value) => {
                    this.plugin.settings.groupByDate = value;
                    await this.plugin.saveSettings();
                }));

        new Setting(containerEl)
            .setName('自动同步')
            .setDesc('启用自动同步')
            .addToggle(toggle => toggle
                .setValue(this.plugin.settings.autoSync)
                .onChange(async (value) => {
                    this.plugin.settings.autoSync = value;
                    await this.plugin.saveSettings();
                    if (value) {
                        this.plugin.startAutoSync();
                    } else {
                        this.plugin.stopAutoSync();
                    }
                }));

        new Setting(containerEl)
            .setName('自动同步间隔')
            .setDesc('自动同步的间隔时间（分钟）')
            .addSlider(slider => slider
                .setLimits(5, 120, 5)
                .setValue(this.plugin.settings.autoSyncInterval)
                .setDynamicTooltip()
                .onChange(async (value) => {
                    this.plugin.settings.autoSyncInterval = value;
                    await this.plugin.saveSettings();
                    if (this.plugin.settings.autoSync) {
                        this.plugin.restartAutoSync();
                    }
                }));

        new Setting(containerEl)
            .setName('上次同步')
            .setDesc('上次成功同步的时间')
            .addText(text => text
                .setValue(this.plugin.settings.lastSyncTime || '从未同步')
                .setDisabled(true))
            .addButton(button => button
                .setButtonText('重置')
                .setTooltip('手动设置同步起始时间')
                .onClick(() => {
                    new ResetSyncTimestampModal(this.app, async (newTimestamp) => {
                        this.plugin.settings.lastSyncTimestamp = newTimestamp;
                        this.plugin.settings.lastSyncTime = new Date(newTimestamp).toLocaleString();
                        await this.plugin.saveSettings();
                        this.display(); // Refresh UI
                        new Notice('同步起始时间已成功重置！');
                    }).open();
                }));
                
        if (this.plugin.settings.lastSyncTimestamp > 0) {
            new Setting(containerEl)
                .setName('上次同步时间戳')
                .setDesc('上次同步的时间戳（毫秒）')
                .addText(text => text
                    .setValue(this.plugin.settings.lastSyncTimestamp.toString())
                    .setDisabled(true));
        }

        new Setting(containerEl)
            .setName('测试连接')
            .setDesc('测试与服务的连接')
            .addButton(button => button
                .setButtonText('测试')
                .onClick(async () => {
                    let success = false;
                    const s3Client = new S3ApiClient(this.plugin.settings);
                    success = await s3Client.testConnection();
                    
                    if (success) {
                        new Notice('S3 连接成功！');
                    } else {
                        new Notice('S3 连接失败，请检查配置信息');
                    }
                }));

        new Setting(containerEl)
            .setName('立即同步')
            .setDesc('立即从服务器同步文档')
            .addButton(button => button
                .setButtonText('同步')
                .onClick(async () => {
                    await this.plugin.syncDocuments();
                }));
        
        containerEl.createEl('h3', { text: '支持与联系' });
        
        const bannerContainer = containerEl.createDiv({ cls: 'mp-support-banner-container' });
        bannerContainer.createEl('img', {
            attr: {
                src: 'https://res.cloudinary.com/dwal1nlws/image/upload/v1751077342/rasbz7k3x8maw8hgmoz6.bmp'
            },
            cls: 'mp-support-banner-image'
        });
    }

    displayFaq(containerEl: HTMLElement): void {
        containerEl.empty();
        containerEl.createEl('h2', { text: '常见问题 (FAQ)' });

        // Question 1
        const q1 = containerEl.createEl('div', { cls: 'faq-item' });
        q1.createEl('h3', { text: '1. 提示连接失败需要怎么做？' });
        const p1a = q1.createEl('p');
        p1a.createEl('strong', { text: '原因一：检查图床 CORS 跨域配置' });
        p1a.appendText('，缤纷云的跨域配置如下：');
        q1.createEl('img', { 
            attr: { src: 'https://res.cloudinary.com/dwal1nlws/image/upload/v1751080692/yrcg2nojpjdm862tnt4p.jpg' },
            cls: 'faq-image'
        });
        
        const p1b = q1.createEl('p');
        p1b.createEl('strong', { text: '原因二：检查子用户权限' });
        p1b.appendText('，缤纷云的子用户权限配置如下：');
        q1.createEl('img', { 
            attr: { src: 'https://res.cloudinary.com/dwal1nlws/image/upload/v1751080843/ei7orbw7oxgroiuf16sy.jpg' },
            cls: 'faq-image'
        });

        // Question 2
        const q2 = containerEl.createEl('div', { cls: 'faq-item' });
        q2.createEl('h3', { text: '2. 【文件路径前缀】和【同步文件夹】两个配置项有什么区别？' });
        const p2 = q2.createEl('p');
        p2.innerHTML = '【文件路径前缀】是指从 S3 中同步的路径，例如 S3 存储桶中有两个文件夹 <code>mpclipper</code>、<code>obclipper</code>。你选择的路径前缀是 <code>obclipper</code>，则只会同步 <code>obclipper</code> 文件夹下的内容到你的 Obsidian 仓库中。<br><br>【同步文件夹】是指你要把 S3 中的内容同步到你 Obsidian 仓库中的哪个文件夹下，我建议单独设置一个同步文件夹，例如 <code>obclipper</code>、<code>docsync</code> 等。';

        // Question 3
        const q3 = containerEl.createEl('div', { cls: 'faq-item' });
        q3.createEl('h3', { text: '3. 已经同步文件并编辑后，如果点击重置或者重装插件后，重新开始同步所有文件会导致本地文件被覆盖吗？' });
        const p3 = q3.createEl('p');
        p3.innerHTML = '基于尊重用户数据的原则，当本地文件存在时，会跳过该文件的同步。如果你就是想全量拉取一份到本地，可以更改【同步文件夹】。<br><br>事实上，我个人倾向于将剪藏文件夹作为一个单纯的【收集箱】，只负责采集数据的中转站，当你需要处理文档时，移动文档到你的【工作区】吧。';
    }

    /**
     * 备份当前配置到文件
     */
    async onBackup() {
        try {
            // 1. 创建设置的深拷贝，避免直接修改原始设置
            const backupSettings = JSON.parse(JSON.stringify(this.plugin.settings));
            
            // 2. 转换为格式化的 JSON 字符串
            const jsonString = JSON.stringify(backupSettings, null, 2);
            
            // 3. 生成带时间戳的文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `mpclipper-sync-backup-${timestamp}.json`;
            
            // 4. 使用 Vault Adapter 写入文件
            await this.app.vault.adapter.write(filename, jsonString);
            
            new Notice(`✅ 配置已成功备份到: ${filename}\n请妥善保管此文件，它可能包含敏感信息。`);
        } catch (error) {
            console.error('备份配置失败:', error);
            new Notice('❌ 备份配置失败，请检查控制台获取更多信息。');
        }
    }

    /**
     * 从剪贴板导入配置
     */
    async onImport() {
        new ImportConfigModal(this.app, (jsonString) => {
            this.applyImport(jsonString);
        }).open();
    }
    
    /**
     * 应用导入的配置
     * @param jsonString 包含配置的 JSON 字符串
     */
    private async applyImport(jsonString: string) {
        try {
            const importedSettings = JSON.parse(jsonString);
            const currentSettings = this.plugin.settings;
            
            // 智能合并
            for (const key in importedSettings) {
                if (Object.prototype.hasOwnProperty.call(importedSettings, key)) {
                    const typedKey = key as keyof DocumentSyncerSettings;
                    if (typedKey === 'lastSyncTimestamp' || typedKey === 'lastSyncTime') {
                        if (!currentSettings[typedKey] || currentSettings[typedKey] === 0) {
                            (currentSettings as any)[typedKey] = importedSettings[typedKey];
                        }
                    } else if (typedKey in currentSettings) {
                        (currentSettings as any)[typedKey] = importedSettings[typedKey];
                    }
                }
            }
            
            await this.plugin.saveSettings();
            this.display(); // 刷新设置页面
            new Notice('✅ 配置导入成功！');
            
        } catch (error) {
            console.error('导入配置失败:', error);
            new Notice('❌ 导入失败：无效的 JSON 格式或数据不匹配。');
        }
    }
}

class ImportConfigModal extends Modal {
    private onSave: (result: string) => void;
    private jsonInput: string = '';

    constructor(app: App, onSave: (result: string) => void) {
        super(app);
        this.onSave = onSave;
    }

    onOpen() {
        const { contentEl } = this;
        contentEl.empty();
        contentEl.addClass('mpclipper-import-modal');

        contentEl.createEl('h2', { text: '从剪贴板导入配置' });

        const warningEl = contentEl.createDiv({ cls: 'mpclipper-warning' });
        warningEl.createEl('strong', { text: '⚠️ 警告:' });
        warningEl.createEl('p', { text: '此操作将覆盖您当前的配置（同步历史除外）。强烈建议您在继续之前先备份当前配置。' });

        const textEl = contentEl.createEl('textarea', {
            placeholder: '请在此处粘贴您的 JSON 配置...',
        });
        textEl.rows = 10;
        textEl.style.width = '100%';
        textEl.addEventListener('input', (e) => {
            this.jsonInput = (e.target as HTMLTextAreaElement).value;
        });

        const buttonContainer = contentEl.createDiv({ cls: 'modal-button-container' });

        new Setting(buttonContainer)
            .addButton(button => button
                .setButtonText('导入')
                .setCta() // Call to action
                .onClick(() => {
                    if (!this.jsonInput.trim()) {
                        new Notice('请输入 JSON 配置。');
                        return;
                    }
                    this.onSave(this.jsonInput);
                    this.close();
                }))
            .addButton(button => button
                .setButtonText('取消')
                .onClick(() => this.close()));
    }

    onClose() {
        const { contentEl } = this;
        contentEl.empty();
    }
}

class ResetSyncTimestampModal extends Modal {
    private onSave: (result: number) => void;
    private selectedDate: string;
    private selectedTime: string;

    constructor(app: App, onSave: (result: number) => void) {
        super(app);
        this.onSave = onSave;
        
        // 初始化为当前日期和时间
        const now = new Date();
        this.selectedDate = now.toISOString().split('T')[0];
        this.selectedTime = now.toTimeString().split(' ')[0].substring(0, 5);
    }

    onOpen() {
        const { contentEl } = this;
        contentEl.empty();
        contentEl.createEl('h2', { text: '重置同步起始时间' });

        new Setting(contentEl)
            .setName('选择日期')
            .addText(text => {
                text.inputEl.type = 'date';
                text.setValue(this.selectedDate)
                    .onChange(value => this.selectedDate = value);
            });
            
        new Setting(contentEl)
            .setName('选择时间')
            .addText(text => {
                text.inputEl.type = 'time';
                text.setValue(this.selectedTime)
                    .onChange(value => this.selectedTime = value);
            });

        new Setting(contentEl)
            .addButton(button => button
                .setButtonText('保存')
                .setCta()
                .onClick(() => {
                    const newTimestamp = new Date(`${this.selectedDate}T${this.selectedTime}`).getTime();
                    this.onSave(newTimestamp);
                    this.close();
                }))
            .addButton(button => button
                .setButtonText('取消')
                .onClick(() => this.close()));
    }

    onClose() {
        const { contentEl } = this;
        contentEl.empty();
    }
}
