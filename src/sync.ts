import { Notice, TFile, TFolder, Vault } from 'obsidian';
import { S3ApiClient } from './s3Client';
import { Document, DocumentSyncerSettings, S3Object } from './types';

export class DocumentSyncer {
    private settings: DocumentSyncerSettings;
    private vault: Vault;
    private s3Client: S3ApiClient;

    constructor(settings: DocumentSyncerSettings, vault: Vault) {
        this.settings = settings;
        this.vault = vault;
        this.s3Client = new S3ApiClient(settings);
    }

    /**
     * 同步文档
     */
    async syncDocuments(): Promise<boolean> {
        try {
            return await this.syncFromS3();
        } catch (error) {
            console.error('同步文档失败:', error);
            new Notice(`同步文档失败: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 从 S3 同步文档
     */
    private async syncFromS3(): Promise<boolean> {
        try {
            // 确保同步文件夹存在
            await this.ensureSyncFolderExists();
            
            // 获取上次同步时间戳，如果没有则使用 0（从未同步）
            const startTimestamp = this.settings.lastSyncTimestamp || 0;
            
            // 获取文件列表
            const s3Objects = await this.s3Client.listFiles(startTimestamp);
            
            if (s3Objects.length === 0) {
                new Notice('没有新文档需要同步');
                return true;
            }
            
            new Notice(`开始从 S3 同步 ${s3Objects.length} 个文档...`);
            
            // 保存文档
            let successCount = 0;
            let skippedCount = 0;
            let latestTimestamp = startTimestamp;
            
            for (const s3Obj of s3Objects) {
                // 下载文件内容
                const content = await this.s3Client.downloadFile(s3Obj.key);
                if (!content) {
                    continue;
                }
                
                // 构建文档对象
                const fileName = s3Obj.key.split('/').pop() || '';
                const title = fileName.replace(/\.md$/, '');
                const doc: Document = {
                    id: s3Obj.key,
                    title: title,
                    content: content,
                    path: this.getRelativePath(s3Obj.key),
                    timestamp: s3Obj.lastModified.toISOString()
                };
                
                // 保存文档
                const result = await this.saveDocument(doc);
                if (result === 'created') {
                    successCount++;
                    
                    // 更新最新时间戳
                    const objTimestamp = s3Obj.lastModified.getTime();
                    if (objTimestamp > latestTimestamp) {
                        latestTimestamp = objTimestamp;
                    }
                } else if (result === 'skipped') {
                    skippedCount++;
                }
            }
            
            // 更新同步状态
            const now = new Date();
            
            // 更新设置
            this.settings.lastSyncTimestamp = latestTimestamp;
            this.settings.lastSyncTime = now.toISOString();
            
            let noticeMessage = `同步完成：成功导入 ${successCount} 个新文档。`;
            if (skippedCount > 0) {
                noticeMessage += `\n${skippedCount} 个文件因已存在而被跳过。`;
            }
            new Notice(noticeMessage);
            return true;
        } catch (error) {
            console.error('从 S3 同步文档失败:', error);
            new Notice(`从 S3 同步文档失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 确保文件夹存在的辅助方法
     * @param folderPath 文件夹路径
     * @param throwOnError 是否在出错时抛出异常
     */
    private async ensureFolderExists(folderPath: string, throwOnError: boolean = false): Promise<void> {
        if (!folderPath) {
            return;
        }

        // 规范化路径，移除末尾的斜杠
        const normalizedPath = folderPath.replace(/\/$/, '');
        
        // 首次检查文件夹是否存在
        let folderExists = this.vault.getAbstractFileByPath(normalizedPath) instanceof TFolder;

        if (!folderExists) {
            try {
                // 对于嵌套文件夹，确保父文件夹存在
                const parentPath = normalizedPath.substring(0, normalizedPath.lastIndexOf('/'));
                if (parentPath && parentPath !== normalizedPath) {
                    await this.ensureFolderExists(parentPath, false);
                }
                
                await this.vault.createFolder(normalizedPath);
                console.log(`成功创建文件夹: ${normalizedPath}`);
            } catch (error) {
                console.warn(`创建文件夹失败: ${normalizedPath}`, error);
                
                // 创建失败后，再次检查文件夹是否已存在（可能是并发创建）
                folderExists = this.vault.getAbstractFileByPath(normalizedPath) instanceof TFolder;
                if (folderExists) {
                    console.log(`文件夹已存在（并发创建检测）: ${normalizedPath}`);
                    return;
                }
                
                // 检查是否是因为文件夹或同名文件已存在而失败
                // 支持多种错误信息格式
                const errorMessage = (error.message || error.toString() || '').toLowerCase();
                const isAlreadyExistsError = 
                    errorMessage.includes('folder already exists') ||
                    errorMessage.includes('file already exists') ||
                    errorMessage.includes('already exists') ||
                    errorMessage.includes('file exists') ||
                    errorMessage.includes('eexist') ||
                    errorMessage.includes('文件夹已存在') ||
                    errorMessage.includes('目录已存在') ||
                    (error.code && error.code.toUpperCase() === 'EEXIST');
                
                if (isAlreadyExistsError) {
                    // 文件夹或同名文件已存在，这是正常情况
                    console.log(`文件夹或同名文件已存在，视为正常: ${normalizedPath}`);
                    return;
                }
                
                // 其他错误根据参数决定是否抛出
                if (throwOnError) {
                    throw new Error(`创建文件夹失败: ${error.message}`);
                } else {
                    console.warn(`创建文件夹失败，但继续执行: ${normalizedPath}`, error);
                }
            }
        } else {
            console.log(`文件夹已存在: ${normalizedPath}`);
        }
    }

    /**
     * 确保同步文件夹存在
     */
    private async ensureSyncFolderExists(): Promise<void> {
        if (!this.settings.syncFolder) {
            return;
        }

        try {
            await this.ensureFolderExists(this.settings.syncFolder, true);
        } catch (error) {
            throw new Error(`创建同步文件夹失败: ${error.message}`);
        }
    }

    /**
     * 保存文档到 Obsidian 库
     * @returns 'created' 如果文件被创建, 'skipped' 如果文件已存在, 'failed' 如果发生错误
     */
    private async saveDocument(doc: Document): Promise<'created' | 'skipped' | 'failed'> {
        try {
            // 构建文件路径
            let filePath = doc.path;
            if (this.settings.syncFolder) {
                // 如果设置了同步文件夹，将文档保存到该文件夹下
                filePath = `${this.settings.syncFolder}/${doc.path}`;
            }

            // 检查文件是否已存在
            const existingFile = this.vault.getAbstractFileByPath(filePath);
            if (existingFile instanceof TFile) {
                // 如果文件已存在，则跳过
                return 'skipped';
            }
            
            // 确保文件夹存在
            const folderPath = filePath.substring(0, filePath.lastIndexOf('/'));
            if (folderPath) {
                await this.ensureFolderExists(folderPath, true);
            }

            // 创建新文件
            await this.vault.create(filePath, doc.content);
            return 'created';

        } catch (error) {
            const errorMessage = (error.message || error.toString() || '').toLowerCase();
            const isAlreadyExistsError = 
                errorMessage.includes('file already exists') ||
                errorMessage.includes('folder already exists');

            if (isAlreadyExistsError) {
                // 如果是文件/文件夹已存在的冲突，静默处理并标记为跳过
                console.warn(`因文件/文件夹名称冲突，跳过保存文档 ${doc.title}:`, error);
                return 'skipped';
            }

            // 对于其他所有真正的错误，才弹窗提示
            console.error(`保存文档 ${doc.title} 失败:`, error);
            new Notice(`保存文档 ${doc.title} 失败: ${error.message}`);
            return 'failed';
        }
    }
    
    /**
     * 获取相对路径
     * 从 S3 对象键名中提取相对路径
     */
    private getRelativePath(key: string): string {
        // 移除配置中设置的 S3 路径前缀，得到文件在存储桶中的相对路径
        let relativePath = key;
        if (this.settings.s3FilePath && relativePath.startsWith(this.settings.s3FilePath)) {
            relativePath = relativePath.substring(this.settings.s3FilePath.length);
        }

        // 移除路径开头的斜杠
        if (relativePath.startsWith('/')) {
            relativePath = relativePath.substring(1);
        }

        // 如果关闭了"按日期分组"，则只保留文件名
        if (!this.settings.groupByDate) {
            // 使用 pop() 来安全地获取路径的最后一部分（文件名）
            const pathSegments = relativePath.split('/');
            return pathSegments.pop() || '';
        }

        return relativePath;
    }
}
