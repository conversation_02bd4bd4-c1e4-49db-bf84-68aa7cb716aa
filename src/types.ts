// 插件设置接口
export interface DocumentSyncerSettings {
    // S3 配置
    s3Endpoint: string;
    s3Region: string;
    s3BucketName: string;
    s3AccessKey: string;
    s3AccessSecret: string;
    s3FilePath: string;         // S3 文件路径前缀

    // 同步配置
    syncFolder: string;
    groupByDate: boolean;       // 新增配置项：按日期分组

    // 通用配置
    autoSync: boolean;
    autoSyncInterval: number;   // 以分钟为单位
    lastSyncTime: string;
    lastSyncTimestamp: number;  // 上次同步时间戳（毫秒）
}

/**
 * 默认设置
 */
export const DEFAULT_SETTINGS: DocumentSyncerSettings = {
    // S3 配置
    s3Endpoint: '',
    s3Region: '',
    s3BucketName: '',
    s3AccessKey: '',
    s3AccessSecret: '',
    s3FilePath: '',
    
    // 同步配置
    syncFolder: '',
    groupByDate: false,
    
    // 通用配置
    autoSync: false,
    autoSyncInterval: 30, // 默认 30 分钟
    lastSyncTime: '',
    lastSyncTimestamp: 0,
};

// 文档接口
export interface Document {
    id: string;
    title: string;
    content: string;
    path: string;
    timestamp: string;
}

// S3 对象接口
export interface S3Object {
    key: string;           // 对象键名（文件路径）
    lastModified: Date;    // 最后修改时间
    size: number;          // 文件大小
    content?: string;      // 文件内容
}


